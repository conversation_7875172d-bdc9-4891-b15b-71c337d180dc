<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>测试高级编辑功能</title>
  <link rel="stylesheet" href="css/style.css">
  <style>
    .test-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .test-product {
      border: 1px solid #ddd;
      padding: 15px;
      margin: 10px 0;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .product-info h4 {
      margin: 0 0 5px 0;
      color: #333;
    }
    
    .product-info p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
    
    .test-btn {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .test-btn:hover {
      background-color: #0056b3;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h2>高级编辑功能测试</h2>
    <p>点击下面的"高级编辑"按钮来测试新的手风琴式编辑界面：</p>
    
    <div class="test-product">
      <div class="product-info">
        <h4>测试产品 1</h4>
        <p>价格: RM 10.00 | 库存: 100</p>
      </div>
      <button class="test-btn" onclick="testAdvancedEdit()">高级编辑</button>
    </div>
    
    <div class="test-product">
      <div class="product-info">
        <h4>测试产品 2</h4>
        <p>价格: RM 25.50 | 库存: 50</p>
      </div>
      <button class="test-btn" onclick="testAdvancedEdit2()">高级编辑</button>
    </div>
  </div>

  <script src="js/api.js"></script>
  <script src="js/product.js"></script>
  <script>
    // 测试产品数据
    const testProduct1 = {
      id: 1,
      name: "测试产品 1",
      description: "这是一个测试产品的描述",
      price: 10.00,
      cost: 5.00,
      points: 10,
      barcode: "123456789",
      productGroupId: 1,
      productType: 0,
      enableStock: true,
      currentStock: 100,
      minStock: 10,
      disallowSaleIfOutOfStock: false,
      stockAlert: true
    };
    
    const testProduct2 = {
      id: 2,
      name: "测试产品 2",
      description: "另一个测试产品",
      price: 25.50,
      cost: 12.00,
      points: 25,
      barcode: "987654321",
      productGroupId: 2,
      productType: 1,
      enableStock: true,
      currentStock: 50,
      minStock: 5,
      disallowSaleIfOutOfStock: true,
      stockAlert: false
    };
    
    function testAdvancedEdit() {
      showAdvancedEditModal(testProduct1);
    }
    
    function testAdvancedEdit2() {
      showAdvancedEditModal(testProduct2);
    }
    
    // 模拟登录状态
    localStorage.setItem('token', 'test-token');
  </script>
</body>
</html>
