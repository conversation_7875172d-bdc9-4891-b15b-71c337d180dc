<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>测试高级编辑功能</title>
  <link rel="stylesheet" href="css/style.css">
  <style>
    .test-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .test-product {
      border: 1px solid #ddd;
      padding: 15px;
      margin: 10px 0;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .product-info h4 {
      margin: 0 0 5px 0;
      color: #333;
    }
    
    .product-info p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
    
    .test-btn {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .test-btn:hover {
      background-color: #0056b3;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h2>高级编辑功能测试 - 完整版</h2>
    <p>点击下面的"高级编辑"按钮来测试新的功能：</p>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
      <h3>🆕 新功能特性：</h3>
      <ul>
        <li>✅ <strong>PRICING</strong>: 未启用时不可编辑，启用后可设置价格</li>
        <li>✅ <strong>图片管理</strong>: 新增"移除产品图片"功能</li>
        <li>✅ <strong>价格设置</strong>: Price [And/Or] Points 组合输入，带+/-调整按钮</li>
        <li>✅ <strong>界面优化</strong>: 移除了产品类型选择</li>
      </ul>
    </div>

    <div class="test-product">
      <div class="product-info">
        <h4>🍜 MAGGIE KARI KARI KAW</h4>
        <p>价格: RM 4.70 | 积分: Not Set | 库存: 100</p>
        <small>测试完整的用户价格管理功能</small>
      </div>
      <button class="test-btn" onclick="testAdvancedEdit()">高级编辑</button>
    </div>

    <div class="test-product">
      <div class="product-info">
        <h4>🥤 测试饮料产品</h4>
        <p>价格: RM 25.50 | 积分: 10 | 库存: 50</p>
        <small>测试图片上传和移除功能</small>
      </div>
      <button class="test-btn" onclick="testAdvancedEdit2()">高级编辑</button>
    </div>
  </div>

  <script src="js/api.js"></script>
  <script src="js/product.js"></script>
  <script>
    // 测试产品数据
    const testProduct1 = {
      id: 1,
      name: "MAGGIE KARI KARI KAW",
      description: "经典马来西亚即食面，咖喱口味",
      price: 4.70,
      cost: 2.50,
      points: 0,
      pointsPrice: 0,
      purchaseOptions: 0, // And
      barcode: "123456789",
      productGroupId: 1,
      enableStock: true,
      currentStock: 100,
      minStock: 10,
      disallowSaleIfOutOfStock: false,
      stockAlert: true
    };

    const testProduct2 = {
      id: 2,
      name: "测试饮料产品",
      description: "带图片的测试产品，用于测试图片上传和移除功能",
      price: 25.50,
      cost: 12.00,
      points: 0,
      pointsPrice: 10.00,
      purchaseOptions: 1, // Or
      barcode: "987654321",
      productGroupId: 2,
      enableStock: true,
      currentStock: 50,
      minStock: 5,
      disallowSaleIfOutOfStock: true,
      stockAlert: false
    };
    
    function testAdvancedEdit() {
      showAdvancedEditModal(testProduct1);
    }
    
    function testAdvancedEdit2() {
      showAdvancedEditModal(testProduct2);
    }
    
    // 模拟登录状态
    localStorage.setItem('token', 'test-token');
  </script>
</body>
</html>
