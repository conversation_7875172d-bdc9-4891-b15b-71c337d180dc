<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>测试高级编辑功能</title>
  <link rel="stylesheet" href="css/style.css">
  <style>
    .test-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .test-product {
      border: 1px solid #ddd;
      padding: 15px;
      margin: 10px 0;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .product-info h4 {
      margin: 0 0 5px 0;
      color: #333;
    }
    
    .product-info p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
    
    .test-btn {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .test-btn:hover {
      background-color: #0056b3;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h2>高级编辑功能测试 - 完整版</h2>
    <p>点击下面的"高级编辑"按钮来测试新的功能：</p>

    <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
      <h3>🆕 最新优化特性：</h3>
      <ul>
        <li>✅ <strong>GENERAL布局优化</strong>: 移除图片框，重新整理表单布局，更加整齐美观</li>
        <li>✅ <strong>PRICING改进</strong>: 显示所有用户组的价格和积分价格，移除操作列</li>
        <li>✅ <strong>手风琴动画</strong>: 添加丝滑的展开/收起动画效果</li>
        <li>✅ <strong>价格设置</strong>: Price [And/Or] Points 组合输入，带+/-调整按钮</li>
        <li>✅ <strong>界面简化</strong>: 移除了产品类型选择和图片上传功能</li>
      </ul>
    </div>

    <div class="test-product">
      <div class="product-info">
        <h4>🍜 MAGGIE KARI KARI KAW</h4>
        <p>价格: RM 4.70 | 积分: Not Set | 库存: 100</p>
        <small>测试优化后的GENERAL布局和PRICING功能</small>
      </div>
      <button class="test-btn" onclick="testAdvancedEdit()">高级编辑</button>
    </div>

    <div class="test-product">
      <div class="product-info">
        <h4>🥤 SERA BANANA MILK</h4>
        <p>价格: RM 8.97 | 积分: 1600 | 库存: 50</p>
        <small>测试手风琴动画和用户价格显示</small>
      </div>
      <button class="test-btn" onclick="testAdvancedEdit2()">高级编辑</button>
    </div>
  </div>

  <script src="js/api.js"></script>
  <script src="js/product.js"></script>
  <script>
    // 测试产品数据
    const testProduct1 = {
      id: 1,
      name: "MAGGIE KARI KARI KAW",
      description: "经典马来西亚即食面，咖喱口味",
      price: 4.70,
      cost: 2.50,
      points: 0,
      pointsPrice: 0,
      purchaseOptions: 0, // And
      barcode: "123456789",
      productGroupId: 1,
      enableStock: true,
      currentStock: 100,
      minStock: 10,
      disallowSaleIfOutOfStock: false,
      stockAlert: true
    };

    const testProduct2 = {
      id: 2,
      name: "SERA BANANA MILK",
      description: "香蕉牛奶饮料，测试用户价格和手风琴动画功能",
      price: 8.97,
      cost: 4.50,
      points: 3,
      pointsPrice: 1600,
      purchaseOptions: 1, // Or
      barcode: "6973388312222",
      productGroupId: 2,
      enableStock: true,
      currentStock: 50,
      minStock: 5,
      disallowSaleIfOutOfStock: true,
      stockAlert: false
    };
    
    function testAdvancedEdit() {
      showAdvancedEditModal(testProduct1);
    }
    
    function testAdvancedEdit2() {
      showAdvancedEditModal(testProduct2);
    }
    
    // 模拟登录状态
    localStorage.setItem('token', 'test-token');
  </script>
</body>
</html>
