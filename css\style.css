/* CSS变量定义 */
:root {
    --primary-color: #4a90e2;
    --primary-hover: #357abd;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;

    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;

    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #868e96;

    --border-color: #dee2e6;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-sm: 4px;

    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 24px rgba(0,0,0,0.2);

    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease;
}

/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    min-height: 100vh;
}

h2, h3 {
    color: #343a40;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    display: inline-block;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

/* 现代化按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    white-space: nowrap;
    width: auto;
    margin: 0;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    border-color: var(--text-secondary);
}

.btn-icon {
    font-size: 1rem;
}

/* 传统按钮样式保持兼容性 */
button:not(.btn) {
    width: 100%;
    background-color: #007bff;
    color: white;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

button:not(.btn):hover {
    background-color: #0056b3;
}

/* 状态样式 */
.error {
    color: var(--danger-color);
    margin-top: 10px;
    font-weight: bold;
}

.no-results {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.error-message {
    text-align: center;
    padding: 2rem;
    color: var(--danger-color);
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* 加载指示器 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    color: var(--text-secondary);
    font-size: 1rem;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse-red {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    }
    50% {
        box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        height: 100vh;
        height: 100dvh; /* 动态视口高度，适配移动端 */
    }

    .header-content {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-right {
        justify-content: center;
    }

    .main-content {
        padding: 1rem;
        overflow: hidden;
    }

    .app-title {
        font-size: 1.3rem;
        justify-content: center;
    }

    .controls-row {
        flex-direction: column;
        gap: 0.75rem;
    }

    .group-selector-container {
        min-width: auto;
    }

    .search-input-wrapper {
        max-width: none;
    }

    .stock-filter {
        justify-content: center;
    }

    .stats-row {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .product-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
        padding: 1rem;
    }

    .product-info {
        text-align: center;
    }

    .product-stock {
        margin-left: 0;
        text-align: center;
    }

    .product-actions {
        opacity: 1;
        visibility: visible;
        margin-left: 0;
        flex-wrap: wrap;
        justify-content: center;
    }

    .stock-input {
        width: 80px;
    }

    .edit-btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0.5rem;
    }

    .main-content {
        padding: 0.5rem;
    }

    .app-title {
        font-size: 1.1rem;
    }

    .product-item {
        padding: 1rem;
    }

    .group-item {
        padding: 1rem;
    }
}

/* 产品页面样式 */
#searchInput {
    margin-bottom: 15px;
    padding: 10px;
    width: 100%;
    max-width: 400px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
}

/* 应用容器 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 顶部导航栏 */
.app-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
}

.app-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.title-icon {
    font-size: 1.8rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 顶部控制区域 */
.top-controls {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
    flex-shrink: 0;
}

.controls-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

/* 产品组选择器 */
.group-selector-container {
    position: relative;
    min-width: 150px;
}

.group-selector {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    font-size: 0.9rem;
    color: var(--text-primary);
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.group-selector:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.dropdown-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
    font-size: 0.8rem;
}

/* 搜索输入框 */
.search-input-wrapper {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1rem;
    color: var(--text-secondary);
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    background: var(--bg-primary);
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.search-loading {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
}

/* 库存筛选 */
.stock-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.stock-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.stock-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    cursor: pointer;
    margin: 0;
}

/* 排序选择器 */
.sort-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    font-size: 0.9rem;
    color: var(--text-primary);
    cursor: pointer;
    min-width: 150px;
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

/* 统计信息行 */
.stats-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.item-count {
    font-weight: 500;
}

.unit-count {
    color: var(--text-muted);
}

/* 网格布局 */
.group-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 关闭按钮样式 */
.close-button {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
    color: white;
    padding: 1rem 1.5rem;
    text-align: center;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    grid-column: 1 / -1; /* 占满整行 */
    margin-bottom: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.close-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    border-color: rgba(255, 255, 255, 0.3);
}

.close-icon {
    font-size: 1.3rem;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-text {
    font-size: 1rem;
    letter-spacing: 0.5px;
}

/* 产品列表 */
.product-list {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

.product-list:empty {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 自定义滚动条样式 */
.product-list::-webkit-scrollbar {
    width: 8px;
}

.product-list::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.product-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
    transition: var(--transition);
}

.product-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Firefox滚动条样式 */
.product-list {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--bg-secondary);
}

/* 产品项 */
.product-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    cursor: pointer;
    background: var(--bg-primary);
    flex-wrap: wrap;
    gap: 1rem;
}

.product-item:last-child {
    border-bottom: none;
}

.product-item:hover {
    background: var(--bg-secondary);
}

.product-item:hover .product-actions {
    opacity: 1;
    visibility: visible;
}

/* 产品图标/缩略图 */
.product-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-sm);
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    font-size: 1.2rem;
}

.product-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
}

/* 产品信息 */
.product-info {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
}

.product-details {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

.product-price {
    color: var(--text-primary);
    font-weight: 500;
}

/* 库存信息 */
.product-stock {
    text-align: right;
    flex-shrink: 0;
    margin-left: 1rem;
}

.stock-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    display: block;
    line-height: 1.2;
}

.stock-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 库存状态样式 */
.stock-number.stock-zero {
    color: var(--danger-color);
}

.stock-number.stock-disabled {
    color: var(--text-muted);
    font-style: italic;
}

.stock-number.stock-low {
    color: var(--warning-color);
}

/* 产品操作区域 */
.product-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    flex-shrink: 0;
}

.stock-input {
    width: 60px;
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    text-align: center;
}

.stock-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-stock-btn {
    background: var(--success-color);
    color: white;
}

.add-stock-btn:hover {
    background: #218838;
    transform: scale(1.1);
}

.remove-stock-btn {
    background: var(--danger-color);
    color: white;
}

.remove-stock-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

.edit-btn {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.quick-edit-btn:hover {
    background: var(--info-color);
    color: white;
    border-color: var(--info-color);
}

.advanced-edit-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.empty-state-subtext {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.group-button {
    padding: 15px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    font-size: 14px;
}

.group-button:hover {
    background-color: #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-button[data-expanded="true"],
.group-button.expanded {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.product-list {
    margin-top: 20px;
}

/* 现代化产品卡片 - 垂直列表样式 */
.product-item {
    background: var(--bg-primary);
    padding: 1.5rem;
    margin-bottom: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.product-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.05), transparent);
    transition: left 0.5s;
}

.product-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.product-item:hover::before {
    left: 100%;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.stock-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stock-input {
    width: 80px;
    padding: 5px;
}

.add-stock-btn, .remove-stock-btn, .set-stock-btn {
    padding: 5px 10px;
    width: auto;
    font-size: 12px;
}

.add-stock-btn {
    background-color: #28a745; /* 绿色 */
}

.add-stock-btn:hover {
    background-color: #218838;
}

.remove-stock-btn {
    background-color: #dc3545; /* 红色 */
}

.remove-stock-btn:hover {
    background-color: #c82333;
}

.set-stock-btn {
    background-color: #ffc107; /* 黄色 */
    color: #212529;
}

.set-stock-btn:hover {
    background-color: #e0a800;
}

.product-info {
    background-color: #f8f9fa;
    padding: 15px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

.product-info div {
    margin-bottom: 5px;
}

#logoutBtn {
    width: auto;
    background-color: #dc3545;
    padding: 10px 20px;
    margin-top: 20px;
}

#logoutBtn:hover {
    background-color: #c82333;
}

.loading {
    text-align: center;
    font-size: 18px;
    color: #6c757d;
    margin: 20px 0;
}

.edit-btn, .edit-form button {
    padding: 5px 10px;
    font-size: 12px;
    margin-left: 10px;
    width: auto;
}

.edit-form {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 10px;
    align-items: center;
}

.edit-form label {
    font-weight: bold;
    text-align: right;
}

.edit-form input {
    margin: 0;
    padding: 8px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.edit-form button {
    grid-column: 1 / -1; /* 让按钮横跨两列 */
}

/* 库存显示样式 */
.stock-display {
    font-weight: 600;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
    min-width: 80px;
    text-align: center;
    transition: all 0.3s ease;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.stock-normal {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    color: #2d5a2d;
    border: 1px solid #c3e6c3;
    box-shadow: 0 2px 4px rgba(45, 90, 45, 0.1);
}

.stock-zero {
    background: linear-gradient(135deg, #ffe6e6 0%, #fff0f0 100%);
    color: #d32f2f;
    border: 1px solid #ffcdd2;
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    animation: pulse-red 2s infinite;
}

.stock-disabled {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    color: #666;
    border: 1px solid #ccc;
    box-shadow: 0 2px 4px rgba(102, 102, 102, 0.1);
    font-style: italic;
}

@keyframes pulse-red {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    }
    50% {
        box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
    }
}

/* 库存控制区域样式优化 */
.stock-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.stock-control .stock-input {
    width: 60px;
    padding: 4px 6px;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.stock-control button {
    padding: 4px 8px;
    font-size: 11px;
    margin: 0;
    width: auto;
    min-width: 50px;
}