<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品库存管理系统</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="app-title">
            <span class="title-icon">📦</span>
            产品库存管理
          </h1>
        </div>
        <div class="header-right">
          <a href="add-product.html" class="btn btn-primary">
            <span class="btn-icon">➕</span>
            添加产品
          </a>
          <button id="logoutBtn" class="btn btn-outline">
            <span class="btn-icon">🚪</span>
            登出
          </button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 顶部控制区域 -->
      <div class="top-controls">
        <div class="controls-row">
          <!-- 产品组选择器 -->
          <div class="group-selector-container">
            <select id="groupSelector" class="group-selector">
              <option value="">产品组</option>
            </select>
            <span class="dropdown-icon">▼</span>
          </div>

          <!-- 搜索框 -->
          <div class="search-input-wrapper">
            <span class="search-icon">🔍</span>
            <input type="text" id="searchInput" placeholder="Search by name, barcode..." class="search-input" />
            <div class="search-loading" id="searchLoading" style="display: none;">
              <div class="loading-spinner"></div>
            </div>
          </div>

          <!-- 库存筛选 -->
          <div class="stock-filter">
            <input type="checkbox" id="inStockFilter" class="stock-checkbox">
            <label for="inStockFilter" class="stock-label">In Stock</label>
          </div>

          <!-- 排序按钮 -->
          <button id="sortBtn" class="sort-btn" title="排序">
            <span class="sort-icon">⇅</span>
          </button>
        </div>

        <!-- 统计信息 -->
        <div class="stats-row">
          <span id="itemCount" class="item-count">0 items</span>
          <span id="unitCount" class="unit-count">0 units</span>
        </div>
      </div>

      <!-- 加载指示器 -->
      <div id="loadingIndicator" class="loading-indicator" style="display: none;">
        <div class="loading-spinner"></div>
        <span>正在加载数据...</span>
      </div>

      <!-- 产品列表 -->
      <div id="productList" class="product-list"></div>
    </main>
  </div>

  <script src="js/api.js"></script>
  <script src="js/product.js"></script>
</body>
</html>
